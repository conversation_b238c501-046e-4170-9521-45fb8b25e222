import * as React from 'react';
import type { ChildTextInputProps } from './types';
declare const TextInputFlat: ({ disabled, editable, label, error, selectionColor: customSelectionColor, cursorColor, underlineColor, underlineStyle, activeUnderlineColor, textColor, dense, style, theme, render, multiline, parentState, innerRef, onFocus, forceFocus, onBlur, onChangeText, onLayoutAnimatedText, onLabelTextLayout, onLeftAffixLayoutChange, onRightAffixLayoutChange, onInputLayout, left, right, placeholderTextColor, testID, contentStyle, scaledLabel, ...rest }: ChildTextInputProps) => React.JSX.Element;
export default TextInputFlat;
//# sourceMappingURL=TextInputFlat.d.ts.map