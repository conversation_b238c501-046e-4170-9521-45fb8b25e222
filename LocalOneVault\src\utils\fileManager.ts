import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import * as Camera from 'expo-camera';
import { Platform } from 'react-native';
import { ImageInfo, imageStorage } from './storage';

// Constants
const VAULT_FOLDER = 'LocalOneVault';
const IMAGE_QUALITY = 0.8;

// Get the vault directory path
const getVaultDirectory = (): string => {
  return `${FileSystem.documentDirectory}${VAULT_FOLDER}/`;
};

// Ensure vault directory exists
const ensureVaultDirectory = async (): Promise<void> => {
  const vaultDir = getVaultDirectory();
  const dirInfo = await FileSystem.getInfoAsync(vaultDir);
  
  if (!dirInfo.exists) {
    await FileSystem.makeDirectoryAsync(vaultDir, { intermediates: true });
  }
};

// Generate unique filename
const generateUniqueFilename = (extension: string = 'jpg'): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `vault_${timestamp}_${random}.${extension}`;
};

// Get file extension from URI
const getFileExtension = (uri: string): string => {
  const extension = uri.split('.').pop()?.toLowerCase();
  return extension || 'jpg';
};

// Copy image to vault directory
const copyImageToVault = async (sourceUri: string): Promise<string> => {
  await ensureVaultDirectory();
  
  const extension = getFileExtension(sourceUri);
  const filename = generateUniqueFilename(extension);
  const destinationUri = `${getVaultDirectory()}${filename}`;
  
  await FileSystem.copyAsync({
    from: sourceUri,
    to: destinationUri,
  });
  
  return destinationUri;
};

// File Manager Functions
export const fileManager = {
  // Request camera permissions
  async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return false;
    }
  },

  // Request media library permissions
  async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  },

  // Take photo with camera
  async takePhoto(): Promise<ImageInfo | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) {
        throw new Error('Camera permission denied');
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: IMAGE_QUALITY,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const asset = result.assets[0];
      const vaultUri = await copyImageToVault(asset.uri);
      
      const imageInfo: ImageInfo = {
        id: Date.now().toString(),
        uri: vaultUri,
        filename: vaultUri.split('/').pop() || 'unknown',
        timestamp: Date.now(),
      };

      await imageStorage.addImage(imageInfo);
      return imageInfo;
    } catch (error) {
      console.error('Error taking photo:', error);
      throw error;
    }
  },

  // Pick image from library
  async pickImageFromLibrary(): Promise<ImageInfo | null> {
    try {
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) {
        throw new Error('Media library permission denied');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: IMAGE_QUALITY,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const asset = result.assets[0];
      const vaultUri = await copyImageToVault(asset.uri);
      
      const imageInfo: ImageInfo = {
        id: Date.now().toString(),
        uri: vaultUri,
        filename: vaultUri.split('/').pop() || 'unknown',
        timestamp: Date.now(),
      };

      await imageStorage.addImage(imageInfo);
      return imageInfo;
    } catch (error) {
      console.error('Error picking image from library:', error);
      throw error;
    }
  },

  // Delete image from vault
  async deleteImage(imageInfo: ImageInfo): Promise<void> {
    try {
      // Remove from file system
      const fileInfo = await FileSystem.getInfoAsync(imageInfo.uri);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(imageInfo.uri);
      }

      // Remove from storage
      await imageStorage.removeImage(imageInfo.id);
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  },

  // Get all images from vault
  async getAllImages(): Promise<ImageInfo[]> {
    try {
      const imageList = await imageStorage.getImageList();
      
      // Verify that files still exist and filter out missing ones
      const validImages: ImageInfo[] = [];
      
      for (const imageInfo of imageList) {
        const fileInfo = await FileSystem.getInfoAsync(imageInfo.uri);
        if (fileInfo.exists) {
          validImages.push(imageInfo);
        } else {
          // Remove from storage if file doesn't exist
          await imageStorage.removeImage(imageInfo.id);
        }
      }
      
      return validImages.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Error getting all images:', error);
      return [];
    }
  },

  // Clear all images from vault
  async clearAllImages(): Promise<void> {
    try {
      const images = await this.getAllImages();
      
      // Delete all image files
      for (const imageInfo of images) {
        const fileInfo = await FileSystem.getInfoAsync(imageInfo.uri);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(imageInfo.uri);
        }
      }

      // Clear the image list from storage
      await imageStorage.clearImageList();
    } catch (error) {
      console.error('Error clearing all images:', error);
      throw error;
    }
  },

  // Get vault directory info
  async getVaultInfo(): Promise<{ exists: boolean; size?: number; imageCount: number }> {
    try {
      const vaultDir = getVaultDirectory();
      const dirInfo = await FileSystem.getInfoAsync(vaultDir);
      const images = await this.getAllImages();
      
      return {
        exists: dirInfo.exists,
        size: dirInfo.exists ? dirInfo.size : undefined,
        imageCount: images.length,
      };
    } catch (error) {
      console.error('Error getting vault info:', error);
      return { exists: false, imageCount: 0 };
    }
  },

  // Check if image exists
  async imageExists(uri: string): Promise<boolean> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      return fileInfo.exists;
    } catch (error) {
      console.error('Error checking if image exists:', error);
      return false;
    }
  },
};
