import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  HelperText,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '../constants/colors';
import { pinStorage, securityStorage, appStorage } from '../utils/storage';

interface SetupPinScreenProps {
  onSetupComplete: () => void;
}

const SECURITY_QUESTIONS = [
  "What was the name of your first pet?",
  "What city were you born in?",
  "What was your childhood nickname?",
  "What is your mother's maiden name?",
  "What was the name of your elementary school?",
  "What was your favorite food as a child?",
  "What was the model of your first car?",
  "What street did you grow up on?",
];

export default function SetupPinScreen({ onSetupComplete }: SetupPinScreenProps) {
  const [step, setStep] = useState<'pin' | 'confirm' | 'security'>('pin');
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [securityQuestion, setSecurityQuestion] = useState('');
  const [securityAnswer, setSecurityAnswer] = useState('');
  const [loading, setLoading] = useState(false);
  const [showQuestions, setShowQuestions] = useState(false);

  const validatePin = (pinValue: string): string | null => {
    if (pinValue.length < 4) {
      return 'PIN must be at least 4 digits';
    }
    if (pinValue.length > 6) {
      return 'PIN must be at most 6 digits';
    }
    if (!/^\d+$/.test(pinValue)) {
      return 'PIN must contain only numbers';
    }
    return null;
  };

  const handlePinSubmit = () => {
    const error = validatePin(pin);
    if (error) {
      Alert.alert('Invalid PIN', error);
      return;
    }
    setStep('confirm');
  };

  const handleConfirmPinSubmit = () => {
    if (pin !== confirmPin) {
      Alert.alert('PIN Mismatch', 'The PINs you entered do not match. Please try again.');
      setConfirmPin('');
      return;
    }
    setStep('security');
  };

  const handleSecuritySetup = async () => {
    if (!securityQuestion.trim()) {
      Alert.alert('Missing Question', 'Please select a security question.');
      return;
    }
    if (!securityAnswer.trim()) {
      Alert.alert('Missing Answer', 'Please provide an answer to the security question.');
      return;
    }
    if (securityAnswer.trim().length < 2) {
      Alert.alert('Invalid Answer', 'Security answer must be at least 2 characters long.');
      return;
    }

    setLoading(true);
    try {
      // Save PIN and security question
      await pinStorage.setPin(pin);
      await securityStorage.setSecurityQuestion(securityQuestion, securityAnswer);
      await appStorage.setFirstLaunchComplete();
      
      Alert.alert(
        'Setup Complete!',
        'Your LocalOne Vault has been secured with your PIN and security question.',
        [{ text: 'Continue', onPress: onSetupComplete }]
      );
    } catch (error) {
      console.error('Error setting up PIN:', error);
      Alert.alert('Setup Error', 'Failed to save your security settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderPinStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Create Your PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Choose a 4-6 digit PIN to secure your vault
        </Text>
        
        <TextInput
          mode="outlined"
          label="Enter PIN"
          value={pin}
          onChangeText={setPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <HelperText type="info" visible={true}>
          Your PIN will be used to access your secure photo vault
        </HelperText>
        
        <Button
          mode="contained"
          onPress={handlePinSubmit}
          style={styles.button}
          disabled={pin.length < 4}
        >
          Continue
        </Button>
      </Card.Content>
    </Card>
  );

  const renderConfirmStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Confirm Your PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Please enter your PIN again to confirm
        </Text>
        
        <TextInput
          mode="outlined"
          label="Confirm PIN"
          value={confirmPin}
          onChangeText={setConfirmPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <View style={styles.buttonRow}>
          <Button
            mode="outlined"
            onPress={() => {
              setStep('pin');
              setConfirmPin('');
            }}
            style={[styles.button, styles.buttonHalf]}
          >
            Back
          </Button>
          <Button
            mode="contained"
            onPress={handleConfirmPinSubmit}
            style={[styles.button, styles.buttonHalf]}
            disabled={confirmPin.length < 4}
          >
            Continue
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const renderSecurityStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Security Question
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Set up a security question to recover your PIN if needed
        </Text>
        
        <TextInput
          mode="outlined"
          label="Security Question"
          value={securityQuestion}
          onChangeText={setSecurityQuestion}
          style={styles.input}
          right={
            <TextInput.Icon
              icon="chevron-down"
              onPress={() => setShowQuestions(!showQuestions)}
            />
          }
          onFocus={() => setShowQuestions(true)}
        />
        
        {showQuestions && (
          <Card style={styles.questionsCard}>
            <Card.Content>
              {SECURITY_QUESTIONS.map((question, index) => (
                <View key={index}>
                  <Button
                    mode="text"
                    onPress={() => {
                      setSecurityQuestion(question);
                      setShowQuestions(false);
                    }}
                    style={styles.questionButton}
                    contentStyle={styles.questionButtonContent}
                  >
                    <Text variant="bodyMedium" style={styles.questionText}>
                      {question}
                    </Text>
                  </Button>
                  {index < SECURITY_QUESTIONS.length - 1 && <Divider />}
                </View>
              ))}
            </Card.Content>
          </Card>
        )}
        
        <TextInput
          mode="outlined"
          label="Your Answer"
          value={securityAnswer}
          onChangeText={setSecurityAnswer}
          style={styles.input}
          autoCapitalize="words"
        />
        
        <HelperText type="info" visible={true}>
          Remember your answer - it will be needed to recover your PIN
        </HelperText>
        
        <View style={styles.buttonRow}>
          <Button
            mode="outlined"
            onPress={() => setStep('confirm')}
            style={[styles.button, styles.buttonHalf]}
          >
            Back
          </Button>
          <Button
            mode="contained"
            onPress={handleSecuritySetup}
            style={[styles.button, styles.buttonHalf]}
            loading={loading}
            disabled={!securityQuestion.trim() || !securityAnswer.trim()}
          >
            Complete Setup
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="displaySmall" style={styles.appTitle}>
              LocalOne Vault
            </Text>
            <Text variant="bodyLarge" style={styles.welcomeText}>
              Secure your photos with privacy
            </Text>
          </View>
          
          {step === 'pin' && renderPinStep()}
          {step === 'confirm' && renderConfirmStep()}
          {step === 'security' && renderSecurityStep()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.md,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    marginTop: spacing.lg,
  },
  appTitle: {
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
  },
  welcomeText: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  card: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
  },
  title: {
    color: colors.onSurface,
    fontWeight: typography.fontWeights.semibold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  input: {
    marginBottom: spacing.sm,
    backgroundColor: colors.inputBackground,
  },
  inputContent: {
    color: colors.onSurface,
  },
  button: {
    marginTop: spacing.md,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  buttonHalf: {
    flex: 1,
  },
  questionsCard: {
    backgroundColor: colors.surfaceVariant,
    marginBottom: spacing.sm,
    maxHeight: 200,
  },
  questionButton: {
    justifyContent: 'flex-start',
    paddingVertical: spacing.sm,
  },
  questionButtonContent: {
    justifyContent: 'flex-start',
  },
  questionText: {
    color: colors.onSurface,
    textAlign: 'left',
  },
});
