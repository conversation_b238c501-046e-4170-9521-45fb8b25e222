import React, { useState, useEffect } from "react";
import { View, StyleSheet } from "react-native";
import { Text, ActivityIndicator } from "react-native-paper";
import { colors } from "../constants/colors";
import { pinStorage, appStorage } from "../utils/storage";

// Import screens
import SetupPinScreen from "../screens/SetupPinScreen";
import LockScreen from "../screens/LockScreen";
import VaultScreen from "../screens/VaultScreen";
import ChangePinScreen from "../screens/ChangePinScreen";
import RestorePinScreen from "../screens/RestorePinScreen";

// App states
type AppState = "loading" | "setup" | "locked" | "unlocked";

export default function AppNavigator() {
  const [appState, setAppState] = useState<AppState>("loading");
  const [currentScreen, setCurrentScreen] = useState<string>("");

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check if this is the first launch
      const isFirstLaunch = await appStorage.isFirstLaunch();

      if (isFirstLaunch) {
        setAppState("setup");
        return;
      }

      // Check if PIN exists
      const hasPin = await pinStorage.hasPin();

      if (!hasPin) {
        setAppState("setup");
      } else {
        setAppState("locked");
      }
    } catch (error) {
      console.error("Error initializing app:", error);
      // Default to setup if there's an error
      setAppState("setup");
    }
  };

  const handleSetupComplete = () => {
    setAppState("unlocked");
  };

  const handleUnlock = () => {
    setAppState("unlocked");
  };

  const handleLock = () => {
    setAppState("locked");
  };

  const handleChangePin = () => {
    setCurrentScreen("ChangePin");
  };

  const handleForgotPin = () => {
    setCurrentScreen("RestorePin");
  };

  const handleBackToVault = () => {
    setCurrentScreen("");
  };

  const handlePinChanged = () => {
    setCurrentScreen("");
    // Could show a success message or navigate back
  };

  const handlePinRestored = () => {
    setCurrentScreen("");
    setAppState("unlocked");
  };

  // Loading screen
  if (appState === "loading") {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="bodyLarge" style={styles.loadingText}>
          Loading LocalOne Vault...
        </Text>
      </View>
    );
  }

  // Setup flow
  if (appState === "setup") {
    return <SetupPinScreen onSetupComplete={handleSetupComplete} />;
  }

  // Locked state
  if (appState === "locked") {
    return currentScreen === "RestorePin" ? (
      <RestorePinScreen
        onBack={handleBackToVault}
        onPinRestored={handlePinRestored}
      />
    ) : (
      <LockScreen onUnlock={handleUnlock} onForgotPin={handleForgotPin} />
    );
  }

  // Unlocked state - Main app navigation
  return currentScreen === "ChangePin" ? (
    <ChangePinScreen
      onBack={handleBackToVault}
      onPinChanged={handlePinChanged}
    />
  ) : (
    <VaultScreen onLock={handleLock} onChangePin={handleChangePin} />
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background,
    gap: 16,
  },
  loadingText: {
    color: colors.onSurface,
    textAlign: "center",
  },
});
