export const colors = {
  // Primary colors - Professional dark theme
  primary: '#6366F1', // Indigo
  primaryDark: '#4F46E5',
  primaryLight: '#818CF8',
  
  // Background colors
  background: '#0F0F23', // Very dark blue
  surface: '#1A1A2E', // Dark blue-gray
  surfaceVariant: '#16213E', // Slightly lighter dark blue
  
  // Text colors
  onBackground: '#FFFFFF',
  onSurface: '#E2E8F0',
  onSurfaceVariant: '#94A3B8',
  onPrimary: '#FFFFFF',
  
  // Accent colors
  accent: '#10B981', // Emerald green
  accentLight: '#34D399',
  
  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray50: '#F8FAFC',
  gray100: '#F1F5F9',
  gray200: '#E2E8F0',
  gray300: '#CBD5E1',
  gray400: '#94A3B8',
  gray500: '#64748B',
  gray600: '#475569',
  gray700: '#334155',
  gray800: '#1E293B',
  gray900: '#0F172A',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  
  // Border colors
  border: '#334155',
  borderLight: '#475569',
  
  // Input colors
  inputBackground: '#1E293B',
  inputBorder: '#475569',
  inputFocused: '#6366F1',
  
  // Card colors
  card: '#1A1A2E',
  cardBorder: '#334155',
};

export const theme = {
  colors: {
    primary: colors.primary,
    primaryContainer: colors.primaryDark,
    secondary: colors.accent,
    secondaryContainer: colors.accentLight,
    tertiary: colors.info,
    surface: colors.surface,
    surfaceVariant: colors.surfaceVariant,
    background: colors.background,
    error: colors.error,
    errorContainer: colors.error,
    onPrimary: colors.onPrimary,
    onPrimaryContainer: colors.white,
    onSecondary: colors.white,
    onSecondaryContainer: colors.white,
    onTertiary: colors.white,
    onSurface: colors.onSurface,
    onSurfaceVariant: colors.onSurfaceVariant,
    onError: colors.white,
    onErrorContainer: colors.white,
    onBackground: colors.onBackground,
    outline: colors.border,
    outlineVariant: colors.borderLight,
    inverseSurface: colors.white,
    inverseOnSurface: colors.black,
    inversePrimary: colors.primaryLight,
    shadow: colors.black,
    scrim: colors.overlay,
    backdrop: colors.overlay,
  },
  roundness: 12,
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const typography = {
  fontSizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  fontWeights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};
