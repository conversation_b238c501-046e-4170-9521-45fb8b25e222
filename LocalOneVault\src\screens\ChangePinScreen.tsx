import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  HelperText,
  Appbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '../constants/colors';
import { pinStorage } from '../utils/storage';

interface ChangePinScreenProps {
  onBack: () => void;
  onPinChanged: () => void;
}

export default function ChangePinScreen({ onBack, onPinChanged }: ChangePinScreenProps) {
  const [step, setStep] = useState<'current' | 'new' | 'confirm'>('current');
  const [currentPin, setCurrentPin] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [loading, setLoading] = useState(false);

  const validatePin = (pinValue: string): string | null => {
    if (pinValue.length < 4) {
      return 'PIN must be at least 4 digits';
    }
    if (pinValue.length > 6) {
      return 'PIN must be at most 6 digits';
    }
    if (!/^\d+$/.test(pinValue)) {
      return 'PIN must contain only numbers';
    }
    return null;
  };

  const handleCurrentPinSubmit = async () => {
    const error = validatePin(currentPin);
    if (error) {
      Alert.alert('Invalid PIN', error);
      return;
    }

    setLoading(true);
    try {
      const isValid = await pinStorage.verifyPin(currentPin);
      if (isValid) {
        setStep('new');
      } else {
        Alert.alert('Incorrect PIN', 'The current PIN you entered is incorrect.');
        setCurrentPin('');
      }
    } catch (error) {
      console.error('Error verifying current PIN:', error);
      Alert.alert('Error', 'Failed to verify current PIN. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleNewPinSubmit = () => {
    const error = validatePin(newPin);
    if (error) {
      Alert.alert('Invalid PIN', error);
      return;
    }

    if (newPin === currentPin) {
      Alert.alert('Same PIN', 'Your new PIN must be different from your current PIN.');
      return;
    }

    setStep('confirm');
  };

  const handleConfirmPinSubmit = async () => {
    if (newPin !== confirmPin) {
      Alert.alert('PIN Mismatch', 'The PINs you entered do not match. Please try again.');
      setConfirmPin('');
      return;
    }

    setLoading(true);
    try {
      await pinStorage.setPin(newPin);
      Alert.alert(
        'PIN Changed Successfully',
        'Your PIN has been updated successfully.',
        [{ text: 'OK', onPress: onPinChanged }]
      );
    } catch (error) {
      console.error('Error changing PIN:', error);
      Alert.alert('Error', 'Failed to change PIN. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderCurrentPinStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Enter Current PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Please enter your current PIN to continue
        </Text>
        
        <TextInput
          mode="outlined"
          label="Current PIN"
          value={currentPin}
          onChangeText={setCurrentPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <Button
          mode="contained"
          onPress={handleCurrentPinSubmit}
          style={styles.button}
          loading={loading}
          disabled={currentPin.length < 4}
        >
          Continue
        </Button>
      </Card.Content>
    </Card>
  );

  const renderNewPinStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Enter New PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Choose a new 4-6 digit PIN for your vault
        </Text>
        
        <TextInput
          mode="outlined"
          label="New PIN"
          value={newPin}
          onChangeText={setNewPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <HelperText type="info" visible={true}>
          Your new PIN must be different from your current PIN
        </HelperText>
        
        <View style={styles.buttonRow}>
          <Button
            mode="outlined"
            onPress={() => {
              setStep('current');
              setNewPin('');
            }}
            style={[styles.button, styles.buttonHalf]}
          >
            Back
          </Button>
          <Button
            mode="contained"
            onPress={handleNewPinSubmit}
            style={[styles.button, styles.buttonHalf]}
            disabled={newPin.length < 4}
          >
            Continue
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const renderConfirmStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Confirm New PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Please enter your new PIN again to confirm
        </Text>
        
        <TextInput
          mode="outlined"
          label="Confirm New PIN"
          value={confirmPin}
          onChangeText={setConfirmPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <View style={styles.buttonRow}>
          <Button
            mode="outlined"
            onPress={() => {
              setStep('new');
              setConfirmPin('');
            }}
            style={[styles.button, styles.buttonHalf]}
          >
            Back
          </Button>
          <Button
            mode="contained"
            onPress={handleConfirmPinSubmit}
            style={[styles.button, styles.buttonHalf]}
            loading={loading}
            disabled={confirmPin.length < 4}
          >
            Change PIN
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Change PIN" titleStyle={styles.headerTitle} />
      </Appbar.Header>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {step === 'current' && renderCurrentPinStep()}
          {step === 'new' && renderNewPinStep()}
          {step === 'confirm' && renderConfirmStep()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.surface,
    elevation: 4,
  },
  headerTitle: {
    color: colors.onSurface,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.md,
    justifyContent: 'center',
  },
  card: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
  },
  title: {
    color: colors.onSurface,
    fontWeight: typography.fontWeights.semibold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  input: {
    marginBottom: spacing.sm,
    backgroundColor: colors.inputBackground,
  },
  inputContent: {
    color: colors.onSurface,
  },
  button: {
    marginTop: spacing.md,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  buttonHalf: {
    flex: 1,
  },
});
