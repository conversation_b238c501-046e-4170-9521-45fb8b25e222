import * as React from "react";
import { PaperProvider } from "react-native-paper";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import "react-native-reanimated";

import { theme } from "../src/constants/colors";
import AppNavigator from "../src/navigation/AppNavigator";

export default function RootLayout() {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppNavigator />
        <StatusBar style="light" backgroundColor={theme.colors.background} />
      </PaperProvider>
    </SafeAreaProvider>
  );
}
