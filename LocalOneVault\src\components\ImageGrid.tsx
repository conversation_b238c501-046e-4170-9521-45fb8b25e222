import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  Alert,
  Modal,
  Image as RNImage,
} from 'react-native';
import {
  Text,
  Card,
  IconButton,
  Button,
  Portal,
  Dialog,
} from 'react-native-paper';
import { Image } from 'expo-image';
import { colors, spacing } from '../constants/colors';
import { ImageInfo } from '../utils/storage';
import { fileManager } from '../utils/fileManager';

interface ImageGridProps {
  images: ImageInfo[];
  onImageDeleted: (imageId: string) => void;
  onRefresh?: () => void;
  refreshing?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');
const GRID_PADDING = spacing.md;
const GRID_GAP = spacing.sm;
const COLUMNS = 3;
const IMAGE_SIZE = (screenWidth - GRID_PADDING * 2 - GRID_GAP * (COLUMNS - 1)) / COLUMNS;

export default function ImageGrid({ 
  images, 
  onImageDeleted, 
  onRefresh, 
  refreshing = false 
}: ImageGridProps) {
  const [selectedImage, setSelectedImage] = useState<ImageInfo | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingImage, setDeletingImage] = useState<ImageInfo | null>(null);
  const [deleting, setDeleting] = useState(false);

  const handleImagePress = (image: ImageInfo) => {
    setSelectedImage(image);
  };

  const handleImageLongPress = (image: ImageInfo) => {
    setDeletingImage(image);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingImage) return;

    setDeleting(true);
    try {
      await fileManager.deleteImage(deletingImage);
      onImageDeleted(deletingImage.id);
      setShowDeleteDialog(false);
      setDeletingImage(null);
    } catch (error) {
      console.error('Error deleting image:', error);
      Alert.alert('Error', 'Failed to delete image. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  const formatDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderImageItem = ({ item }: { item: ImageInfo }) => (
    <TouchableOpacity
      onPress={() => handleImagePress(item)}
      onLongPress={() => handleImageLongPress(item)}
      style={styles.imageContainer}
      activeOpacity={0.8}
    >
      <Card style={styles.imageCard}>
        <Image
          source={{ uri: item.uri }}
          style={styles.image}
          contentFit="cover"
          transition={200}
          placeholder={{ blurhash: 'L6PZfSi_.AyE_3t7t7R**0o#DgR4' }}
        />
        <View style={styles.imageOverlay}>
          <Text variant="bodySmall" style={styles.imageDate}>
            {formatDate(item.timestamp)}
          </Text>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text variant="headlineSmall" style={styles.emptyTitle}>
        No Photos Yet
      </Text>
      <Text variant="bodyMedium" style={styles.emptySubtitle}>
        Tap the + button to add your first secure photo
      </Text>
    </View>
  );

  const renderImageViewer = () => (
    <Modal
      visible={selectedImage !== null}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setSelectedImage(null)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text variant="titleMedium" style={styles.modalTitle}>
            {selectedImage ? formatDate(selectedImage.timestamp) : ''}
          </Text>
          <IconButton
            icon="close"
            iconColor={colors.white}
            size={24}
            onPress={() => setSelectedImage(null)}
          />
        </View>
        
        {selectedImage && (
          <View style={styles.modalImageContainer}>
            <Image
              source={{ uri: selectedImage.uri }}
              style={styles.modalImage}
              contentFit="contain"
              transition={200}
            />
          </View>
        )}
        
        <View style={styles.modalActions}>
          <Button
            mode="outlined"
            onPress={() => {
              if (selectedImage) {
                setSelectedImage(null);
                handleImageLongPress(selectedImage);
              }
            }}
            style={styles.modalButton}
            textColor={colors.error}
            buttonColor="transparent"
          >
            Delete
          </Button>
        </View>
      </View>
    </Modal>
  );

  const renderDeleteDialog = () => (
    <Portal>
      <Dialog
        visible={showDeleteDialog}
        onDismiss={() => {
          if (!deleting) {
            setShowDeleteDialog(false);
            setDeletingImage(null);
          }
        }}
      >
        <Dialog.Title>Delete Photo</Dialog.Title>
        <Dialog.Content>
          <Text variant="bodyMedium">
            Are you sure you want to delete this photo? This action cannot be undone.
          </Text>
        </Dialog.Content>
        <Dialog.Actions>
          <Button
            onPress={() => {
              setShowDeleteDialog(false);
              setDeletingImage(null);
            }}
            disabled={deleting}
          >
            Cancel
          </Button>
          <Button
            onPress={handleDeleteConfirm}
            loading={deleting}
            textColor={colors.error}
          >
            Delete
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={images}
        renderItem={renderImageItem}
        keyExtractor={(item) => item.id}
        numColumns={COLUMNS}
        contentContainerStyle={[
          styles.gridContainer,
          images.length === 0 && styles.emptyGridContainer,
        ]}
        showsVerticalScrollIndicator={false}
        onRefresh={onRefresh}
        refreshing={refreshing}
        ListEmptyComponent={renderEmptyState}
        ItemSeparatorComponent={() => <View style={{ height: GRID_GAP }} />}
        columnWrapperStyle={images.length > 0 ? styles.row : undefined}
      />
      
      {renderImageViewer()}
      {renderDeleteDialog()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gridContainer: {
    padding: GRID_PADDING,
  },
  emptyGridContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  row: {
    justifyContent: 'space-between',
  },
  imageContainer: {
    width: IMAGE_SIZE,
    height: IMAGE_SIZE,
    marginBottom: GRID_GAP,
  },
  imageCard: {
    flex: 1,
    backgroundColor: colors.surface,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.overlay,
    padding: spacing.xs,
  },
  imageDate: {
    color: colors.white,
    fontSize: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    color: colors.onSurface,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.black,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingTop: spacing.xl,
    paddingBottom: spacing.sm,
  },
  modalTitle: {
    color: colors.white,
    flex: 1,
  },
  modalImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImage: {
    width: '100%',
    height: '100%',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  modalButton: {
    borderColor: colors.error,
  },
});
