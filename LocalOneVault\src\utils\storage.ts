import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Storage keys
const STORAGE_KEYS = {
  PIN: 'vault_pin',
  SECURITY_QUESTION: 'security_question',
  SECURITY_ANSWER: 'security_answer',
  IMAGE_LIST: 'image_list',
  IS_FIRST_LAUNCH: 'is_first_launch',
  PIN_ATTEMPTS: 'pin_attempts',
  LAST_ATTEMPT_TIME: 'last_attempt_time',
} as const;

// Types
export interface SecurityQuestion {
  question: string;
  answer: string;
}

export interface ImageInfo {
  id: string;
  uri: string;
  filename: string;
  timestamp: number;
}

// Utility function to choose storage method based on platform and data sensitivity
const secureStore = {
  async setItem(key: string, value: string): Promise<void> {
    if (Platform.OS === 'web') {
      // Fallback to AsyncStorage on web (less secure but functional)
      await AsyncStorage.setItem(key, value);
    } else {
      await SecureStore.setItemAsync(key, value);
    }
  },

  async getItem(key: string): Promise<string | null> {
    if (Platform.OS === 'web') {
      return await AsyncStorage.getItem(key);
    } else {
      return await SecureStore.getItemAsync(key);
    }
  },

  async deleteItem(key: string): Promise<void> {
    if (Platform.OS === 'web') {
      await AsyncStorage.removeItem(key);
    } else {
      await SecureStore.deleteItemAsync(key);
    }
  },
};

// PIN Management
export const pinStorage = {
  async setPin(pin: string): Promise<void> {
    // Hash the PIN for additional security (simple hash for demo)
    const hashedPin = btoa(pin + 'vault_salt');
    await secureStore.setItem(STORAGE_KEYS.PIN, hashedPin);
  },

  async verifyPin(pin: string): Promise<boolean> {
    try {
      const storedPin = await secureStore.getItem(STORAGE_KEYS.PIN);
      if (!storedPin) return false;
      
      const hashedPin = btoa(pin + 'vault_salt');
      return storedPin === hashedPin;
    } catch (error) {
      console.error('Error verifying PIN:', error);
      return false;
    }
  },

  async hasPin(): Promise<boolean> {
    try {
      const pin = await secureStore.getItem(STORAGE_KEYS.PIN);
      return pin !== null;
    } catch (error) {
      console.error('Error checking PIN existence:', error);
      return false;
    }
  },

  async deletePin(): Promise<void> {
    await secureStore.deleteItem(STORAGE_KEYS.PIN);
  },
};

// Security Question Management
export const securityStorage = {
  async setSecurityQuestion(question: string, answer: string): Promise<void> {
    const hashedAnswer = btoa(answer.toLowerCase().trim() + 'security_salt');
    await secureStore.setItem(STORAGE_KEYS.SECURITY_QUESTION, question);
    await secureStore.setItem(STORAGE_KEYS.SECURITY_ANSWER, hashedAnswer);
  },

  async getSecurityQuestion(): Promise<string | null> {
    return await secureStore.getItem(STORAGE_KEYS.SECURITY_QUESTION);
  },

  async verifySecurityAnswer(answer: string): Promise<boolean> {
    try {
      const storedAnswer = await secureStore.getItem(STORAGE_KEYS.SECURITY_ANSWER);
      if (!storedAnswer) return false;
      
      const hashedAnswer = btoa(answer.toLowerCase().trim() + 'security_salt');
      return storedAnswer === hashedAnswer;
    } catch (error) {
      console.error('Error verifying security answer:', error);
      return false;
    }
  },

  async hasSecurityQuestion(): Promise<boolean> {
    try {
      const question = await secureStore.getItem(STORAGE_KEYS.SECURITY_QUESTION);
      return question !== null;
    } catch (error) {
      console.error('Error checking security question existence:', error);
      return false;
    }
  },
};

// Image List Management
export const imageStorage = {
  async getImageList(): Promise<ImageInfo[]> {
    try {
      const imageListJson = await AsyncStorage.getItem(STORAGE_KEYS.IMAGE_LIST);
      return imageListJson ? JSON.parse(imageListJson) : [];
    } catch (error) {
      console.error('Error getting image list:', error);
      return [];
    }
  },

  async addImage(imageInfo: ImageInfo): Promise<void> {
    try {
      const currentList = await this.getImageList();
      const updatedList = [imageInfo, ...currentList];
      await AsyncStorage.setItem(STORAGE_KEYS.IMAGE_LIST, JSON.stringify(updatedList));
    } catch (error) {
      console.error('Error adding image to list:', error);
      throw error;
    }
  },

  async removeImage(imageId: string): Promise<void> {
    try {
      const currentList = await this.getImageList();
      const updatedList = currentList.filter(img => img.id !== imageId);
      await AsyncStorage.setItem(STORAGE_KEYS.IMAGE_LIST, JSON.stringify(updatedList));
    } catch (error) {
      console.error('Error removing image from list:', error);
      throw error;
    }
  },

  async clearImageList(): Promise<void> {
    await AsyncStorage.removeItem(STORAGE_KEYS.IMAGE_LIST);
  },
};

// App State Management
export const appStorage = {
  async isFirstLaunch(): Promise<boolean> {
    try {
      const isFirst = await AsyncStorage.getItem(STORAGE_KEYS.IS_FIRST_LAUNCH);
      return isFirst === null;
    } catch (error) {
      console.error('Error checking first launch:', error);
      return true;
    }
  },

  async setFirstLaunchComplete(): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.IS_FIRST_LAUNCH, 'false');
  },
};

// PIN Attempt Tracking (for security)
export const attemptStorage = {
  async getAttempts(): Promise<number> {
    try {
      const attempts = await AsyncStorage.getItem(STORAGE_KEYS.PIN_ATTEMPTS);
      return attempts ? parseInt(attempts, 10) : 0;
    } catch (error) {
      console.error('Error getting attempts:', error);
      return 0;
    }
  },

  async incrementAttempts(): Promise<number> {
    const currentAttempts = await this.getAttempts();
    const newAttempts = currentAttempts + 1;
    await AsyncStorage.setItem(STORAGE_KEYS.PIN_ATTEMPTS, newAttempts.toString());
    await AsyncStorage.setItem(STORAGE_KEYS.LAST_ATTEMPT_TIME, Date.now().toString());
    return newAttempts;
  },

  async resetAttempts(): Promise<void> {
    await AsyncStorage.removeItem(STORAGE_KEYS.PIN_ATTEMPTS);
    await AsyncStorage.removeItem(STORAGE_KEYS.LAST_ATTEMPT_TIME);
  },

  async getLastAttemptTime(): Promise<number> {
    try {
      const time = await AsyncStorage.getItem(STORAGE_KEYS.LAST_ATTEMPT_TIME);
      return time ? parseInt(time, 10) : 0;
    } catch (error) {
      console.error('Error getting last attempt time:', error);
      return 0;
    }
  },
};
