import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  HelperText,
  Appbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '../constants/colors';
import { pinStorage, securityStorage } from '../utils/storage';

interface RestorePinScreenProps {
  onBack: () => void;
  onPinRestored: () => void;
}

export default function RestorePinScreen({ onBack, onPinRestored }: RestorePinScreenProps) {
  const [step, setStep] = useState<'question' | 'newPin' | 'confirm'>('question');
  const [securityQuestion, setSecurityQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingQuestion, setLoadingQuestion] = useState(true);

  useEffect(() => {
    loadSecurityQuestion();
  }, []);

  const loadSecurityQuestion = async () => {
    try {
      const question = await securityStorage.getSecurityQuestion();
      if (question) {
        setSecurityQuestion(question);
      } else {
        Alert.alert(
          'No Security Question',
          'No security question has been set up for this vault.',
          [{ text: 'OK', onPress: onBack }]
        );
      }
    } catch (error) {
      console.error('Error loading security question:', error);
      Alert.alert('Error', 'Failed to load security question.');
    } finally {
      setLoadingQuestion(false);
    }
  };

  const validatePin = (pinValue: string): string | null => {
    if (pinValue.length < 4) {
      return 'PIN must be at least 4 digits';
    }
    if (pinValue.length > 6) {
      return 'PIN must be at most 6 digits';
    }
    if (!/^\d+$/.test(pinValue)) {
      return 'PIN must contain only numbers';
    }
    return null;
  };

  const handleAnswerSubmit = async () => {
    if (!answer.trim()) {
      Alert.alert('Missing Answer', 'Please provide an answer to the security question.');
      return;
    }

    setLoading(true);
    try {
      const isCorrect = await securityStorage.verifySecurityAnswer(answer);
      if (isCorrect) {
        setStep('newPin');
      } else {
        Alert.alert(
          'Incorrect Answer',
          'The answer you provided is incorrect. Please try again.'
        );
        setAnswer('');
      }
    } catch (error) {
      console.error('Error verifying security answer:', error);
      Alert.alert('Error', 'Failed to verify security answer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleNewPinSubmit = () => {
    const error = validatePin(newPin);
    if (error) {
      Alert.alert('Invalid PIN', error);
      return;
    }
    setStep('confirm');
  };

  const handleConfirmPinSubmit = async () => {
    if (newPin !== confirmPin) {
      Alert.alert('PIN Mismatch', 'The PINs you entered do not match. Please try again.');
      setConfirmPin('');
      return;
    }

    setLoading(true);
    try {
      await pinStorage.setPin(newPin);
      Alert.alert(
        'PIN Restored Successfully',
        'Your new PIN has been set successfully. You can now access your vault.',
        [{ text: 'OK', onPress: onPinRestored }]
      );
    } catch (error) {
      console.error('Error setting new PIN:', error);
      Alert.alert('Error', 'Failed to set new PIN. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderQuestionStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Security Question
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Answer your security question to restore your PIN
        </Text>
        
        <Card style={styles.questionCard}>
          <Card.Content>
            <Text variant="bodyLarge" style={styles.questionText}>
              {securityQuestion}
            </Text>
          </Card.Content>
        </Card>
        
        <TextInput
          mode="outlined"
          label="Your Answer"
          value={answer}
          onChangeText={setAnswer}
          style={styles.input}
          contentStyle={styles.inputContent}
          autoCapitalize="words"
        />
        
        <HelperText type="info" visible={true}>
          Enter the answer exactly as you set it up
        </HelperText>
        
        <Button
          mode="contained"
          onPress={handleAnswerSubmit}
          style={styles.button}
          loading={loading}
          disabled={!answer.trim()}
        >
          Verify Answer
        </Button>
      </Card.Content>
    </Card>
  );

  const renderNewPinStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Set New PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Choose a new 4-6 digit PIN for your vault
        </Text>
        
        <TextInput
          mode="outlined"
          label="New PIN"
          value={newPin}
          onChangeText={setNewPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <HelperText type="info" visible={true}>
          Choose a PIN that you can remember easily
        </HelperText>
        
        <View style={styles.buttonRow}>
          <Button
            mode="outlined"
            onPress={() => {
              setStep('question');
              setNewPin('');
            }}
            style={[styles.button, styles.buttonHalf]}
          >
            Back
          </Button>
          <Button
            mode="contained"
            onPress={handleNewPinSubmit}
            style={[styles.button, styles.buttonHalf]}
            disabled={newPin.length < 4}
          >
            Continue
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const renderConfirmStep = () => (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="headlineMedium" style={styles.title}>
          Confirm New PIN
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Please enter your new PIN again to confirm
        </Text>
        
        <TextInput
          mode="outlined"
          label="Confirm New PIN"
          value={confirmPin}
          onChangeText={setConfirmPin}
          secureTextEntry
          keyboardType="numeric"
          maxLength={6}
          style={styles.input}
          contentStyle={styles.inputContent}
        />
        
        <View style={styles.buttonRow}>
          <Button
            mode="outlined"
            onPress={() => {
              setStep('newPin');
              setConfirmPin('');
            }}
            style={[styles.button, styles.buttonHalf]}
          >
            Back
          </Button>
          <Button
            mode="contained"
            onPress={handleConfirmPinSubmit}
            style={[styles.button, styles.buttonHalf]}
            loading={loading}
            disabled={confirmPin.length < 4}
          >
            Set PIN
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  if (loadingQuestion) {
    return (
      <SafeAreaView style={styles.container}>
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={onBack} />
          <Appbar.Content title="Restore PIN" titleStyle={styles.headerTitle} />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <Text variant="bodyLarge" style={styles.loadingText}>
            Loading security question...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={onBack} />
        <Appbar.Content title="Restore PIN" titleStyle={styles.headerTitle} />
      </Appbar.Header>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {step === 'question' && renderQuestionStep()}
          {step === 'newPin' && renderNewPinStep()}
          {step === 'confirm' && renderConfirmStep()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.surface,
    elevation: 4,
  },
  headerTitle: {
    color: colors.onSurface,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.md,
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.onSurface,
  },
  card: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
  },
  title: {
    color: colors.onSurface,
    fontWeight: typography.fontWeights.semibold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  questionCard: {
    backgroundColor: colors.surfaceVariant,
    marginBottom: spacing.lg,
  },
  questionText: {
    color: colors.onSurface,
    fontWeight: typography.fontWeights.medium,
    textAlign: 'center',
  },
  input: {
    marginBottom: spacing.sm,
    backgroundColor: colors.inputBackground,
  },
  inputContent: {
    color: colors.onSurface,
  },
  button: {
    marginTop: spacing.md,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.sm,
  },
  buttonHalf: {
    flex: 1,
  },
});
