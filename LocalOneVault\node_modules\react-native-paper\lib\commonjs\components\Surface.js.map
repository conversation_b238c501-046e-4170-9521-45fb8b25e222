{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "_overlay", "_shadow", "_interopRequireDefault", "_forwardRef", "_splitStyles", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "MD2Surface", "forwardRef", "style", "theme", "overrideTheme", "rest", "ref", "elevation", "StyleSheet", "flatten", "dark", "isDarkTheme", "mode", "colors", "useInternalTheme", "createElement", "Animated", "View", "backgroundColor", "overlay", "surface", "shadow", "outerLayerStyleProperties", "shadowColor", "iOSShadowOutputRanges", "shadowOpacity", "height", "shadowRadius", "inputRange", "getStyleForShadowLayer", "layer", "isAnimatedValue", "interpolate", "outputRange", "extrapolate", "shadowOffset", "width", "SurfaceIOS", "testID", "children", "container", "props", "outerLayerViewStyles", "innerLayerViewStyles", "useMemo", "flattenedStyles", "filteredStyles", "outerLayerStyles", "borderRadiusStyles", "splitStyles", "includes", "startsWith", "endsWith", "process", "env", "NODE_ENV", "overflow", "console", "warn", "bgColor", "isElevated", "flex", "undefined", "Surface", "overridenTheme", "isV3", "_colors$elevation2", "map", "_colors$elevation", "Platform", "OS", "pointerEvents", "elevationLevel", "getElevationAndroid", "margin", "padding", "transform", "borderRadius", "sharedStyle", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/Surface.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAC,sBAAA,CAAAL,OAAA;AAEA,IAAAM,WAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAAmD,SAAAK,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAb,uBAAA,YAAAA,CAAAS,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AA2CnD,MAAMG,UAAU,GAAG,IAAAC,sBAAU,EAC3B,CAAC;EAAEC,KAAK;EAAEC,KAAK,EAAEC,aAAa;EAAE,GAAGC;AAA+B,CAAC,EAAEC,GAAG,KAAK;EAC3E,MAAM;IAAEC,SAAS,GAAG;EAAE,CAAC,GAAIC,uBAAU,CAACC,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;EACxE,MAAM;IAAEQ,IAAI,EAAEC,WAAW;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAG,IAAAC,yBAAgB,EAACV,aAAa,CAAC;EAE3E,oBACEvC,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI,EAAAvB,QAAA;IACZY,GAAG,EAAEA;EAAI,GACLD,IAAI;IACRH,KAAK,EAAE,CACL;MACEgB,eAAe,EACbP,WAAW,IAAIC,IAAI,KAAK,UAAU,GAC9B,IAAAO,gBAAO,EAACZ,SAAS,EAAEM,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,OAAO,CAAC,GACnCP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO;IAChB,CAAC,EACDb,SAAS,GAAG,IAAAc,eAAM,EAACd,SAAS,CAAC,GAAG,IAAI,EACpCL,KAAK;EACL,EACH,CAAC;AAEN,CACF,CAAC;AAED,MAAMoB,yBAA8C,GAAG,CACrD,UAAU,EACV,WAAW,EACX,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,YAAY,EACZ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,CACV;AAED,MAAMC,WAAW,GAAG,MAAM;AAC1B,MAAMC,qBAAqB,GAAG,CAC5B;EACEC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;AACnC,CAAC,EACD;EACEF,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACjC,CAAC,CACF;AACD,MAAMC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,SAASC,sBAAsBA,CAC7BtB,SAAoB,EACpBuB,KAAY,EACgC;EAC5C,IAAI,IAAAC,wBAAe,EAACxB,SAAS,CAAC,EAAE;IAC9B,OAAO;MACLgB,WAAW;MACXE,aAAa,EAAElB,SAAS,CAACyB,WAAW,CAAC;QACnCJ,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBK,WAAW,EAAE,CAAC,CAAC,EAAET,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,CAAC;QAC5DS,WAAW,EAAE;MACf,CAAC,CAAC;MACFC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRV,MAAM,EAAEnB,SAAS,CAACyB,WAAW,CAAC;UAC5BJ,UAAU;UACVK,WAAW,EAAET,qBAAqB,CAACM,KAAK,CAAC,CAACJ;QAC5C,CAAC;MACH,CAAC;MACDC,YAAY,EAAEpB,SAAS,CAACyB,WAAW,CAAC;QAClCJ,UAAU;QACVK,WAAW,EAAET,qBAAqB,CAACM,KAAK,CAAC,CAACH;MAC5C,CAAC;IACH,CAAC;EACH;EAEA,OAAO;IACLJ,WAAW;IACXE,aAAa,EAAElB,SAAS,GAAGiB,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,GAAG,CAAC;IACzEU,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRV,MAAM,EAAEF,qBAAqB,CAACM,KAAK,CAAC,CAACJ,MAAM,CAACnB,SAAS;IACvD,CAAC;IACDoB,YAAY,EAAEH,qBAAqB,CAACM,KAAK,CAAC,CAACH,YAAY,CAACpB,SAAS;EACnE,CAAC;AACH;AAEA,MAAM8B,UAAU,GAAG,IAAApC,sBAAU,EAO3B,CACE;EACEM,SAAS;EACTL,KAAK;EACLgB,eAAe;EACfoB,MAAM;EACNC,QAAQ;EACR3B,IAAI,GAAG,UAAU;EACjB4B,SAAS;EACT,GAAGC;AACL,CAAC,EACDnC,GAAG,KACA;EACH,MAAM,CAACoC,oBAAoB,EAAEC,oBAAoB,CAAC,GAAG9E,KAAK,CAAC+E,OAAO,CAAC,MAAM;IACvE,MAAMC,eAAe,GAAIrC,uBAAU,CAACC,OAAO,CAACP,KAAK,CAAC,IAAI,CAAC,CAAe;IAEtE,MAAM,CAAC4C,cAAc,EAAEC,gBAAgB,EAAEC,kBAAkB,CAAC,GAC1D,IAAAC,wBAAW,EACTJ,eAAe,EACd3C,KAAK,IACJoB,yBAAyB,CAAC4B,QAAQ,CAAChD,KAAK,CAAC,IACzCA,KAAK,CAACiD,UAAU,CAAC,QAAQ,CAAC,EAC3BjD,KAAK,IAAKA,KAAK,CAACiD,UAAU,CAAC,QAAQ,CAAC,IAAIjD,KAAK,CAACkD,QAAQ,CAAC,QAAQ,CAClE,CAAC;IAEH,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCT,cAAc,CAACU,QAAQ,KAAK,QAAQ,IACpCjD,SAAS,KAAK,CAAC,EACf;MACAkD,OAAO,CAACC,IAAI,CACV,uKACF,CAAC;IACH;IAEA,MAAMC,OAAO,GAAGd,eAAe,CAAC3B,eAAe,IAAIA,eAAe;IAElE,MAAM0C,UAAU,GAAGhD,IAAI,KAAK,UAAU;IAEtC,MAAM8B,oBAAoB,GAAG;MAC3B,IAAIkB,UAAU,IAAI/B,sBAAsB,CAACtB,SAAS,EAAE,CAAC,CAAC,CAAC;MACvD,GAAGwC,gBAAgB;MACnB,GAAGC,kBAAkB;MACrB9B,eAAe,EAAEyC;IACnB,CAAC;IAED,MAAMhB,oBAAoB,GAAG;MAC3B,IAAIiB,UAAU,IAAI/B,sBAAsB,CAACtB,SAAS,EAAE,CAAC,CAAC,CAAC;MACvD,GAAGuC,cAAc;MACjB,GAAGE,kBAAkB;MACrBa,IAAI,EACFhB,eAAe,CAACnB,MAAM,IAAK,CAACc,SAAS,IAAIK,eAAe,CAACgB,IAAK,GAC1D,CAAC,GACDC,SAAS;MACf5C,eAAe,EAAEyC;IACnB,CAAC;IAED,OAAO,CAACjB,oBAAoB,EAAEC,oBAAoB,CAAC;EACrD,CAAC,EAAE,CAACzC,KAAK,EAAEK,SAAS,EAAEW,eAAe,EAAEN,IAAI,EAAE4B,SAAS,CAAC,CAAC;EAExD,oBACE3E,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI;IACZX,GAAG,EAAEA,GAAI;IACTJ,KAAK,EAAEwC,oBAAqB;IAC5BJ,MAAM,EAAE,GAAGA,MAAM;EAAe,gBAEhCzE,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI,EAAAvB,QAAA,KAAK+C,KAAK;IAAEvC,KAAK,EAAEyC,oBAAqB;IAACL,MAAM,EAAEA;EAAO,IACnEC,QACY,CACF,CAAC;AAEpB,CACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,OAAO,GAAG,IAAA9D,sBAAU,EACxB,CACE;EACEM,SAAS,GAAG,CAAC;EACbgC,QAAQ;EACRpC,KAAK,EAAE6D,cAAc;EACrB9D,KAAK;EACLoC,MAAM,GAAG,SAAS;EAClB1B,IAAI,GAAG,UAAU;EACjB,GAAG6B;AACE,CAAC,EACRnC,GAAG,KACA;EACH,MAAMH,KAAK,GAAG,IAAAW,yBAAgB,EAACkD,cAAc,CAAC;EAE9C,IAAI,CAAC7D,KAAK,CAAC8D,IAAI,EACb,oBACEpG,KAAA,CAAAkD,aAAA,CAACf,UAAU,EAAAN,QAAA,KAAK+C,KAAK;IAAEtC,KAAK,EAAEA,KAAM;IAACD,KAAK,EAAEA,KAAM;IAACI,GAAG,EAAEA;EAAI,IACzDiC,QACS,CAAC;EAGjB,MAAM;IAAE1B;EAAO,CAAC,GAAGV,KAAK;EAExB,MAAMyB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErC,MAAMV,eAAe,GAAG,CAACgD,kBAAA,IAAM;IAC7B,IAAI,IAAAnC,wBAAe,EAACxB,SAAS,CAAC,EAAE;MAC9B,OAAOA,SAAS,CAACyB,WAAW,CAAC;QAC3BJ,UAAU;QACVK,WAAW,EAAEL,UAAU,CAACuC,GAAG,CAAE5D,SAAS,IAAK;UAAA,IAAA6D,iBAAA;UACzC,QAAAA,iBAAA,GAAOvD,MAAM,CAACN,SAAS,cAAA6D,iBAAA,uBAAhBA,iBAAA,CAAmB,QAAQ7D,SAAS,EAAkB,CAAC;QAChE,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,QAAA2D,kBAAA,GAAOrD,MAAM,CAACN,SAAS,cAAA2D,kBAAA,uBAAhBA,kBAAA,CAAmB,QAAQ3D,SAAS,EAAE,CAAC;EAChD,CAAC,EAAE,CAAC;EAEJ,MAAMqD,UAAU,GAAGhD,IAAI,KAAK,UAAU;EAEtC,IAAIyD,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,MAAM;MAAEC,aAAa,GAAG;IAAO,CAAC,GAAG9B,KAAK;IACxC,oBACE5E,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI,EAAAvB,QAAA,KACR+C,KAAK;MACT8B,aAAa,EAAEA,aAAc;MAC7BjE,GAAG,EAAEA,GAAI;MACTgC,MAAM,EAAEA,MAAO;MACfpC,KAAK,EAAE,CACL;QAAEgB;MAAgB,CAAC,EACnBX,SAAS,IAAIqD,UAAU,GAAG,IAAAvC,eAAM,EAACd,SAAS,EAAEJ,KAAK,CAAC8D,IAAI,CAAC,GAAG,IAAI,EAC9D/D,KAAK;IACL,IAEDqC,QACY,CAAC;EAEpB;EAEA,IAAI8B,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IAC7B,MAAME,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IAE3C,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAI,IAAA1C,wBAAe,EAACxB,SAAS,CAAC,EAAE;QAC9B,OAAOA,SAAS,CAACyB,WAAW,CAAC;UAC3BJ,UAAU;UACVK,WAAW,EAAEuC;QACf,CAAC,CAAC;MACJ;MAEA,OAAOA,cAAc,CAACjE,SAAS,CAAC;IAClC,CAAC;IAED,MAAM;MAAEmE,MAAM;MAAEC,OAAO;MAAEC,SAAS;MAAEC;IAAa,CAAC,GAAIrE,uBAAU,CAACC,OAAO,CACtEP,KACF,CAAC,IAAI,CAAC,CAAe;IAErB,MAAM6C,gBAAgB,GAAG;MAAE2B,MAAM;MAAEC,OAAO;MAAEC,SAAS;MAAEC;IAAa,CAAC;IACrE,MAAMC,WAAW,GAAG,CAAC;MAAE5D;IAAgB,CAAC,EAAEhB,KAAK,CAAC;IAEhD,oBACErC,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAgD,QAAQ,CAACC,IAAI,EAAAvB,QAAA,KACR+C,KAAK;MACTH,MAAM,EAAEA,MAAO;MACfhC,GAAG,EAAEA,GAAI;MACTJ,KAAK,EAAE,CACL;QACEgB,eAAe;QACf0D;MACF,CAAC,EACD7B,gBAAgB,EAChB+B,WAAW,EACXlB,UAAU,IAAI;QACZrD,SAAS,EAAEkE,mBAAmB,CAAC;MACjC,CAAC;IACD,IAEDlC,QACY,CAAC;EAEpB;EAEA,oBACE1E,KAAA,CAAAkD,aAAA,CAACsB,UAAU,EAAA3C,QAAA,KACL+C,KAAK;IACTnC,GAAG,EAAEA,GAAI;IACTC,SAAS,EAAEA,SAAU;IACrBW,eAAe,EAAEA,eAAgB;IACjChB,KAAK,EAAEA,KAAM;IACboC,MAAM,EAAEA,MAAO;IACf1B,IAAI,EAAEA;EAAK,IAEV2B,QACS,CAAC;AAEjB,CACF,CAAC;AAAC,IAAAwC,QAAA,GAAAC,OAAA,CAAAvG,OAAA,GAEasF,OAAO", "ignoreList": []}