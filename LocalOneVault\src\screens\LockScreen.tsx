import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Vibration,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  HelperText,
  IconButton,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '../constants/colors';
import { pinStorage, attemptStorage } from '../utils/storage';

interface LockScreenProps {
  onUnlock: () => void;
  onForgotPin: () => void;
}

const MAX_ATTEMPTS = 5;
const LOCKOUT_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export default function LockScreen({ onUnlock, onForgotPin }: LockScreenProps) {
  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [isLockedOut, setIsLockedOut] = useState(false);
  const [lockoutTimeRemaining, setLockoutTimeRemaining] = useState(0);
  const [showPin, setShowPin] = useState(false);

  useEffect(() => {
    checkLockoutStatus();
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isLockedOut && lockoutTimeRemaining > 0) {
      interval = setInterval(() => {
        setLockoutTimeRemaining((prev) => {
          if (prev <= 1000) {
            setIsLockedOut(false);
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isLockedOut, lockoutTimeRemaining]);

  const checkLockoutStatus = async () => {
    try {
      const currentAttempts = await attemptStorage.getAttempts();
      const lastAttemptTime = await attemptStorage.getLastAttemptTime();
      
      setAttempts(currentAttempts);
      
      if (currentAttempts >= MAX_ATTEMPTS) {
        const timeSinceLastAttempt = Date.now() - lastAttemptTime;
        
        if (timeSinceLastAttempt < LOCKOUT_DURATION) {
          setIsLockedOut(true);
          setLockoutTimeRemaining(LOCKOUT_DURATION - timeSinceLastAttempt);
        } else {
          // Lockout period has expired, reset attempts
          await attemptStorage.resetAttempts();
          setAttempts(0);
        }
      }
    } catch (error) {
      console.error('Error checking lockout status:', error);
    }
  };

  const handlePinSubmit = async () => {
    if (isLockedOut) {
      Alert.alert(
        'Account Locked',
        `Too many failed attempts. Please wait ${formatTime(lockoutTimeRemaining)} before trying again.`
      );
      return;
    }

    if (pin.length < 4) {
      Alert.alert('Invalid PIN', 'Please enter your complete PIN.');
      return;
    }

    setLoading(true);
    
    try {
      const isValid = await pinStorage.verifyPin(pin);
      
      if (isValid) {
        // Reset attempts on successful login
        await attemptStorage.resetAttempts();
        setPin('');
        onUnlock();
      } else {
        // Increment failed attempts
        const newAttempts = await attemptStorage.incrementAttempts();
        setAttempts(newAttempts);
        
        // Vibrate on failed attempt
        Vibration.vibrate(500);
        
        if (newAttempts >= MAX_ATTEMPTS) {
          setIsLockedOut(true);
          setLockoutTimeRemaining(LOCKOUT_DURATION);
          Alert.alert(
            'Account Locked',
            `Too many failed attempts. Your account has been locked for ${LOCKOUT_DURATION / 60000} minutes.`
          );
        } else {
          const remainingAttempts = MAX_ATTEMPTS - newAttempts;
          Alert.alert(
            'Incorrect PIN',
            `Wrong PIN entered. ${remainingAttempts} attempt${remainingAttempts !== 1 ? 's' : ''} remaining.`
          );
        }
        
        setPin('');
      }
    } catch (error) {
      console.error('Error verifying PIN:', error);
      Alert.alert('Error', 'Failed to verify PIN. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const renderPinDots = () => {
    const dots = [];
    for (let i = 0; i < 6; i++) {
      dots.push(
        <View
          key={i}
          style={[
            styles.pinDot,
            i < pin.length ? styles.pinDotFilled : styles.pinDotEmpty,
          ]}
        />
      );
    }
    return <View style={styles.pinDotsContainer}>{dots}</View>;
  };

  const getHelperText = (): string => {
    if (isLockedOut) {
      return `Account locked. Try again in ${formatTime(lockoutTimeRemaining)}`;
    }
    if (attempts > 0) {
      const remaining = MAX_ATTEMPTS - attempts;
      return `${remaining} attempt${remaining !== 1 ? 's' : ''} remaining`;
    }
    return 'Enter your PIN to access your vault';
  };

  const getHelperTextType = (): 'info' | 'error' => {
    return isLockedOut || attempts > 0 ? 'error' : 'info';
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text variant="displaySmall" style={styles.appTitle}>
              LocalOne Vault
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Enter your PIN to unlock
            </Text>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.pinSection}>
                {showPin ? (
                  <TextInput
                    mode="outlined"
                    label="Enter PIN"
                    value={pin}
                    onChangeText={setPin}
                    secureTextEntry={false}
                    keyboardType="numeric"
                    maxLength={6}
                    style={styles.input}
                    contentStyle={styles.inputContent}
                    disabled={isLockedOut}
                    right={
                      <TextInput.Icon
                        icon="eye-off"
                        onPress={() => setShowPin(false)}
                      />
                    }
                  />
                ) : (
                  <>
                    {renderPinDots()}
                    <TextInput
                      mode="outlined"
                      label="Enter PIN"
                      value={pin}
                      onChangeText={setPin}
                      secureTextEntry={true}
                      keyboardType="numeric"
                      maxLength={6}
                      style={styles.input}
                      contentStyle={styles.inputContent}
                      disabled={isLockedOut}
                      right={
                        <TextInput.Icon
                          icon="eye"
                          onPress={() => setShowPin(true)}
                        />
                      }
                    />
                  </>
                )}
              </View>

              <HelperText type={getHelperTextType()} visible={true}>
                {getHelperText()}
              </HelperText>

              <Button
                mode="contained"
                onPress={handlePinSubmit}
                style={styles.unlockButton}
                loading={loading}
                disabled={pin.length < 4 || isLockedOut}
              >
                Unlock Vault
              </Button>

              <Button
                mode="text"
                onPress={onForgotPin}
                style={styles.forgotButton}
                disabled={isLockedOut}
              >
                Forgot PIN?
              </Button>
            </Card.Content>
          </Card>

          <View style={styles.footer}>
            <Text variant="bodySmall" style={styles.footerText}>
              Your photos are stored securely on this device
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: spacing.md,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  appTitle: {
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
  },
  subtitle: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  card: {
    backgroundColor: colors.surface,
    marginBottom: spacing.lg,
  },
  pinSection: {
    marginBottom: spacing.sm,
  },
  pinDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
  },
  pinDotEmpty: {
    borderColor: colors.border,
    backgroundColor: 'transparent',
  },
  pinDotFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
  },
  input: {
    backgroundColor: colors.inputBackground,
  },
  inputContent: {
    color: colors.onSurface,
  },
  unlockButton: {
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  forgotButton: {
    marginTop: spacing.sm,
  },
  footer: {
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  footerText: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
});
