import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  StatusBar,
} from 'react-native';
import {
  Appbar,
  FAB,
  Portal,
  Dialog,
  Button,
  Text,
  Menu,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { colors, spacing } from '../constants/colors';
import { ImageInfo } from '../utils/storage';
import { fileManager } from '../utils/fileManager';
import ImageGrid from '../components/ImageGrid';

interface VaultScreenProps {
  onLock: () => void;
  onChangePin: () => void;
}

export default function VaultScreen({ onLock, onChangePin }: VaultScreenProps) {
  const [images, setImages] = useState<ImageInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [addingImage, setAddingImage] = useState(false);

  // Load images when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadImages();
    }, [])
  );

  const loadImages = async () => {
    try {
      setLoading(true);
      const imageList = await fileManager.getAllImages();
      setImages(imageList);
    } catch (error) {
      console.error('Error loading images:', error);
      Alert.alert('Error', 'Failed to load images. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadImages();
    setRefreshing(false);
  };

  const handleTakePhoto = async () => {
    setShowAddDialog(false);
    setAddingImage(true);
    
    try {
      const newImage = await fileManager.takePhoto();
      if (newImage) {
        setImages(prevImages => [newImage, ...prevImages]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      if (error instanceof Error && error.message.includes('permission')) {
        Alert.alert(
          'Permission Required',
          'Camera permission is required to take photos. Please enable it in your device settings.'
        );
      } else {
        Alert.alert('Error', 'Failed to take photo. Please try again.');
      }
    } finally {
      setAddingImage(false);
    }
  };

  const handlePickFromLibrary = async () => {
    setShowAddDialog(false);
    setAddingImage(true);
    
    try {
      const newImage = await fileManager.pickImageFromLibrary();
      if (newImage) {
        setImages(prevImages => [newImage, ...prevImages]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      if (error instanceof Error && error.message.includes('permission')) {
        Alert.alert(
          'Permission Required',
          'Photo library permission is required to select photos. Please enable it in your device settings.'
        );
      } else {
        Alert.alert('Error', 'Failed to select photo. Please try again.');
      }
    } finally {
      setAddingImage(false);
    }
  };

  const handleImageDeleted = (imageId: string) => {
    setImages(prevImages => prevImages.filter(img => img.id !== imageId));
  };

  const handleClearAllImages = () => {
    Alert.alert(
      'Clear All Photos',
      'Are you sure you want to delete all photos from your vault? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete All',
          style: 'destructive',
          onPress: async () => {
            try {
              await fileManager.clearAllImages();
              setImages([]);
              Alert.alert('Success', 'All photos have been deleted from your vault.');
            } catch (error) {
              console.error('Error clearing images:', error);
              Alert.alert('Error', 'Failed to delete all photos. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleLockVault = () => {
    Alert.alert(
      'Lock Vault',
      'Are you sure you want to lock your vault?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Lock', onPress: onLock },
      ]
    );
  };

  const renderAddDialog = () => (
    <Portal>
      <Dialog
        visible={showAddDialog}
        onDismiss={() => setShowAddDialog(false)}
      >
        <Dialog.Title>Add Photo</Dialog.Title>
        <Dialog.Content>
          <Text variant="bodyMedium" style={styles.dialogText}>
            Choose how you'd like to add a photo to your secure vault:
          </Text>
        </Dialog.Content>
        <Dialog.Actions style={styles.dialogActions}>
          <Button
            mode="outlined"
            onPress={handleTakePhoto}
            style={styles.dialogButton}
            icon="camera"
            disabled={addingImage}
          >
            Take Photo
          </Button>
          <Button
            mode="contained"
            onPress={handlePickFromLibrary}
            style={styles.dialogButton}
            icon="image"
            disabled={addingImage}
          >
            Choose from Library
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={colors.background}
        translucent={false}
      />
      
      <Appbar.Header style={styles.header}>
        <Appbar.Content
          title="LocalOne Vault"
          titleStyle={styles.headerTitle}
        />
        <Menu
          visible={showMenu}
          onDismiss={() => setShowMenu(false)}
          anchor={
            <Appbar.Action
              icon="dots-vertical"
              onPress={() => setShowMenu(true)}
              iconColor={colors.onSurface}
            />
          }
          contentStyle={styles.menu}
        >
          <Menu.Item
            onPress={() => {
              setShowMenu(false);
              onChangePin();
            }}
            title="Change PIN"
            leadingIcon="key-variant"
          />
          <Menu.Item
            onPress={() => {
              setShowMenu(false);
              handleClearAllImages();
            }}
            title="Clear All Photos"
            leadingIcon="delete-sweep"
            disabled={images.length === 0}
          />
          <Menu.Item
            onPress={() => {
              setShowMenu(false);
              handleLockVault();
            }}
            title="Lock Vault"
            leadingIcon="lock"
          />
        </Menu>
      </Appbar.Header>

      <View style={styles.content}>
        <ImageGrid
          images={images}
          onImageDeleted={handleImageDeleted}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      </View>

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => setShowAddDialog(true)}
        loading={addingImage}
        disabled={addingImage}
      />

      {renderAddDialog()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.surface,
    elevation: 4,
  },
  headerTitle: {
    color: colors.primary,
    fontWeight: '600',
  },
  menu: {
    backgroundColor: colors.surface,
  },
  content: {
    flex: 1,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary,
  },
  dialogText: {
    color: colors.onSurface,
    marginBottom: spacing.md,
  },
  dialogActions: {
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: spacing.sm,
  },
  dialogButton: {
    marginHorizontal: 0,
  },
});
